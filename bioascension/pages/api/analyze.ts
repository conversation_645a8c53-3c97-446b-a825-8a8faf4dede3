import { NextApiRequest, NextApiResponse } from 'next';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { quizData } = req.body;

    if (!quizData) {
      return res.status(400).json({ error: 'Quiz data is required' });
    }

    // Format the quiz data for the AI prompt
    const formattedData = formatQuizData(quizData);

    const prompt = `You are GenoBlueprint's AI analyst. Based on detailed quiz inputs from a user aged 12–20, generate a personalized report that predicts their final height, puberty stage, growth plate status, facial maturity, and genetic development trajectory.

The tone should be confident, educational, and actionable, suitable for teens and young adults looking to optimize their growth during puberty. Format it in sections that align with the GenoBlueprint platform:

📏 Final Height Prediction
Predict the user's final height using:
- Parental height
- Current growth rate
- Ethnicity
- Puberty stage
- Lifestyle and hormone factors

Output two sections:

📊 Genetic Height Probability (If Average Habits Continue):
Age: ${quizData.age} | Current: ${quizData.currentHeight}
Provide 5-6 height outcomes with percentage-based probability (e.g., "174 cm – 95%"). 
IMPORTANT: Among these 5-6 results, you MUST include:
- Father's height (calculate from user data)
- Average of father and mother heights (calculate from user data) if it is not less than the current height. Otherwise, skip this.
- Plus 3-4 additional genetic probability outcomes
DO NOT include labels like "(Father's height)" or "(Average of parents)" in the output - just list the heights with percentages.
IMPORTANT: None of the predicted heights should be less than the user's current height. All predicted heights must be greater than the current height.

🚀 Optimized Height Probability (If Growth Is Maximized):
Age: ${quizData.age} | Current: ${quizData.currentHeight}
Provide 5-6 height outcomes with percentage-based probability, but with higher percentages assuming ideal habits like sleep, lean body fat, protein intake, low stress, and active lifestyle.
IMPORTANT: Among these 5-6 results, you MUST include:
- Father's height (calculate from user data)
- Average of father and mother heights (calculate from user data) if it is not less than the current height. Otherwise, skip this.
- Plus 3-4 additional optimized probability outcomes
DO NOT include labels like "(Father's height)" or "(Average of parents)" in the output - just list the heights with percentages.
IMPORTANT: None of the predicted heights should be less than the user's current height. All predicted heights must be greater than the current height.

Start predictions from their current height. Provide a range of possible heights, emphasizing taller outcomes when supported by growth factors, age, parents heights, puberty stage, and lifestyle data.

Then add a short summary sentence explaining the user's potential to reach the upper tier.

🦴 Growth Plate Status
Estimate whether plates are open, narrowing, or mostly fused. Use shoe size trends, joint soreness, puberty timing, and ethnicity. Include a confidence note.

🧬 Puberty Stage
Identify if the user is in early, mid, or late puberty. Show % completion and remaining months. Mention visible indicators like voice, Adam's apple, or facial changes.

💀 Facial Maturity
Estimate how far along their facial bone development is (%). List what has developed and what is still likely to change (e.g., jaw widening, midface projection).

📈 Hormone Trajectory
Assess testosterone status using clues like hair growth, voice depth, acne, sweating, and abs. Explain how current hormone levels support further changes.

🌍 Ethnic Growth Pattern
Reference their background (e.g., Han Chinese, European) and describe average growth timelines. Compare their current path to that baseline.

✅ Recommendations to Maximize Growth
Give 3–6 science-backed tips, personalized from their data. Always include:
- 8+ hours of sleep
- High protein and calcium intake
- Minimize soy, stress, and processed foods
- Maintain low body fat – Explain that low body fat keeps estrogen low, which helps keep growth plates open longer
- Avoid smoke exposure
- Track growth monthly in the morning

User Data:
${formattedData}

Please provide a comprehensive, personalized analysis based on this data.`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are GenoBlueprint's expert AI analyst specializing in growth prediction and puberty development. Provide detailed, accurate, and actionable insights."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const analysis = completion.choices[0]?.message?.content;

    if (!analysis) {
      return res.status(500).json({ error: 'Failed to generate analysis' });
    }

    res.status(200).json({ analysis });
  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze quiz data' });
  }
}

function formatQuizData(quizData: any): string {
  const sections = [
    {
      title: "Basic Information",
      fields: ['biologicalSex', 'age', 'currentHeight', 'currentWeight', 'country']
    },
    {
      title: "Growth & Puberty Markers",
      fields: ['pubertyAge', 'recentGrowthSpurt', 'growthPast12Months', 'growingPains', 'handsFeetsGrowing', 'nightSoreness']
    },
    {
      title: "Skeletal & Facial Development",
      fields: ['shoeSizeIncrease', 'jawlineFacialMaturity', 'adamsApple', 'facialHair', 'cheekbones', 'voiceChange', 'bodyFrameWider']
    },
    {
      title: "Testosterone/Hormone Traits",
      fields: ['bodyHair', 'muscleGain', 'acne', 'voiceDepth', 'sweating', 'armpitGroinHair', 'jawNoseDefinition']
    },
    {
      title: "Genetics & Family Traits",
      fields: ['fatherHeight', 'motherHeight', 'hasSiblings', 'siblingInfo', 'parentsPubertyTiming', 'ethnicBackground', 'familyLateGrowth']
    },
    {
      title: "Lifestyle & Health",
      fields: ['exerciseFrequency', 'sleepHours', 'supplementsMedications', 'dairyConsumption', 'proteinDiet', 'cigaretteSmoke', 'soyConsumption', 'stressLevel', 'visibleAbs']
    },
    {
      title: "Psychological & Developmental Markers",
      fields: ['maturityComparison', 'stillGrowing', 'pubertyStage', 'compareToFamily', 'maturityVsPeers']
    },
    {
      title: "Ethnicity Background",
      fields: ['birthCountry', 'parentsCountries', 'grandparentsCountries', 'ethnicGroup', 'mixedEthnicity']
    },
    {
      title: "Head and Skull Shape",
      fields: ['headShape', 'headTop', 'headBack', 'forehead', 'headWidth', 'raisedLine']
    },
    {
      title: "Face Shape and Features",
      fields: ['faceShape', 'cheekbonesProminence', 'browBone', 'noseShape', 'nostrils', 'eyeSpacing', 'chinShape', 'jawlineShape', 'faceProjection', 'noseUpperLipSpace', 'faceGrowthDirection']
    },
    {
      title: "Eyes and Eyebrows",
      fields: ['eyeColor', 'eyelidFold', 'eyeDepth', 'eyelidType', 'eyebrowShape', 'eyebrowEyeSpace']
    }
  ];

  let formatted = '';
  
  sections.forEach(section => {
    formatted += `\n${section.title}:\n`;
    section.fields.forEach(field => {
      if (quizData[field]) {
        formatted += `- ${field}: ${quizData[field]}\n`;
      }
    });
  });

  return formatted;
} 