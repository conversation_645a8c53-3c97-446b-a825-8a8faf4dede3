import type { NextApiRequest, NextApiResponse } from 'next';
import sgMail from '@sendgrid/mail';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, analysis, name } = req.body;

    if (!email || !analysis) {
      return res.status(400).json({ error: 'Email and analysis are required' });
    }

    // Set SendGrid API key
    const sendGridApiKey = process.env.SENDGRID_API_KEY;
    if (!sendGridApiKey) {
      console.error('SendGrid API key not found');
      return res.status(500).json({ error: 'Email service not configured' });
    }

    sgMail.setApiKey(sendGridApiKey);

    // Load and process email template
    let emailTemplate = fs.readFileSync(path.join(process.cwd(), 'email-template.html'), 'utf8');
    
    // Replace template variables
    emailTemplate = emailTemplate.replace('{{name}}', name || 'there');
    emailTemplate = emailTemplate.replace('{{analysis}}', analysis.replace(/\n/g, '<br>'));
    
    const htmlContent = emailTemplate;

    // Send email using SendGrid
    const msg = {
      to: email,
      from: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      subject: '🧬 Your GenoBlueprint Analysis Report',
      html: htmlContent,
      text: `Hello ${name || 'there'}! Thank you for completing your GenoBlueprint analysis. Your personalized genetic potential report is attached.`,
    };

    try {
      await sgMail.send(msg);
      console.log('📧 Email sent successfully via SendGrid');
      console.log('To:', email);
      console.log('Name:', name);
      console.log('Subject: 🧬 Your GenoBlueprint Analysis Report');
      
      res.status(200).json({ 
        success: true, 
        message: 'Email sent successfully',
        email: email,
        timestamp: new Date().toISOString(),
        service: 'SendGrid'
      });
    } catch (sendGridError) {
      console.error('SendGrid error:', sendGridError);
      
      if (sendGridError.response) {
        console.error('SendGrid response body:', sendGridError.response.body);
      }
      
      res.status(500).json({ 
        error: 'Failed to send email via SendGrid',
        details: sendGridError.message
      });
    }
  } catch (error) {
    console.error('Email sending error:', error);
    res.status(500).json({ error: 'Failed to send email' });
  }
} 