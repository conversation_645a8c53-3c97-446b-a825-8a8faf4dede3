import Head from 'next/head';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';

export default function Success() {
  const router = useRouter();
  const [analysis, setAnalysis] = useState<string>('');
  const [showFullReport, setShowFullReport] = useState(false);
  const [email, setEmail] = useState<string>('');

  useEffect(() => {
    // Get the analysis and email from localStorage
    const storedAnalysis = localStorage.getItem('bioascension_analysis');
    const storedEmail = localStorage.getItem('bioascension_email');
    if (storedAnalysis) {
      setAnalysis(storedAnalysis);
    }
    if (storedEmail) {
      setEmail(storedEmail);
    }
  }, []);

  return (
    <div className="bg-lightblue min-h-screen">
      <Head>
        <title>Success - GenoBlueprint</title>
        <meta name="description" content="Your payment was successful! Your personalized report is on its way." />
      </Head>

      {/* Header */}
      <header className="bg-deepblue text-white shadow">
        <div className="max-w-4xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="font-extrabold text-2xl tracking-tight">
            <span className="bg-gradient-to-r from-teal to-deepblue text-transparent bg-clip-text">GenoBlueprint</span>
          </div>
          <button 
            onClick={() => router.push('/')}
            className="text-teal hover:text-white transition"
          >
            ← Back to Home
          </button>
        </div>
      </header>

      {/* Success Content */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <div className="bg-white rounded-2xl shadow-lg p-12 text-center">
          {/* Success Icon */}
          <div className="w-24 h-24 bg-gradient-to-r from-teal to-deepblue rounded-full flex items-center justify-center mx-auto mb-8">
            <span className="text-4xl text-white">✓</span>
          </div>

          <h1 className="text-3xl font-bold text-deepblue mb-4">Payment Successful!</h1>
          <p className="text-lg text-gray-600 mb-8">
            Thank you for your purchase! Your personalized genetic potential report has been sent to <strong>{email}</strong>.
          </p>

          {/* What Happens Next */}
          <div className="bg-teal bg-opacity-10 border border-teal border-opacity-20 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-bold text-deepblue mb-4">What happens next?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div className="flex items-start">
                <span className="text-2xl mr-3">📊</span>
                <div>
                  <h3 className="font-semibold text-deepblue mb-1">Analysis</h3>
                  <p className="text-sm text-gray-600">Our AI analyzes your quiz responses using advanced genetic algorithms</p>
                </div>
              </div>
              <div className="flex items-start">
                <span className="text-2xl mr-3">📧</span>
                <div>
                  <h3 className="font-semibold text-deepblue mb-1">Email Delivered</h3>
                  <p className="text-sm text-gray-600">Your comprehensive report has been sent to {email}</p>
                </div>
              </div>
              <div className="flex items-start">
                <span className="text-2xl mr-3">🎯</span>
                <div>
                  <h3 className="font-semibold text-deepblue mb-1">Personalized Insights</h3>
                  <p className="text-sm text-gray-600">Receive detailed predictions and actionable recommendations</p>
                </div>
              </div>
            </div>
          </div>

          {/* Report Preview */}
          <div className="text-left mb-8">
            <h2 className="text-xl font-bold text-deepblue mb-4 text-center">Your Report Will Include:</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">📏 Height Predictions</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Final adult height range</li>
                  <li>• Growth timeline projections</li>
                  <li>• Growth plate status assessment</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">🧬 Puberty Analysis</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Current puberty stage</li>
                  <li>• Hormone development timeline</li>
                  <li>• Remaining growth potential</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">💀 Facial Development</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Bone maturity assessment</li>
                  <li>• Facial structure predictions</li>
                  <li>• Jawline development timeline</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">💪 Optimization Tips</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Personalized looksmax strategies</li>
                  <li>• Lifestyle recommendations</li>
                  <li>• Genetic potential maximization</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Full Report Display */}
          {analysis && (
            <div className="text-left mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-deepblue">Your Personalized Analysis</h2>
                <button
                  onClick={() => setShowFullReport(!showFullReport)}
                  className="bg-teal text-white px-4 py-2 rounded-lg hover:bg-teal-600 transition text-sm"
                >
                  {showFullReport ? 'Hide Report' : 'View Full Report'}
                </button>
              </div>
              
              {showFullReport && (
                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                      {analysis}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Contact Support */}
          <div className="border-t border-gray-200 pt-6">
            <p className="text-sm text-gray-600 mb-4">
              Didn't receive your email? Check your spam folder or contact support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="mailto:<EMAIL>"
                className="px-6 py-2 bg-gray-100 text-deepblue rounded-lg hover:bg-gray-200 transition"
              >
                📧 Contact Support
              </a>
              <button 
                onClick={() => router.push('/')}
                className="px-6 py-2 bg-gradient-to-r from-teal to-deepblue text-white rounded-lg hover:from-teal-400 hover:to-deepblue transition"
              >
                Return to Home
              </button>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 text-center text-white">
          <p className="text-sm opacity-80">
            Your data is processed securely and will never be shared with third parties.
          </p>
        </div>
      </div>
    </div>
  );
}
