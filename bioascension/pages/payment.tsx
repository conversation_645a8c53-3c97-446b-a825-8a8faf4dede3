import Head from 'next/head';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

export default function Payment() {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [analysis, setAnalysis] = useState('');

  useEffect(() => {
    // Get the analysis from localStorage
    const storedAnalysis = localStorage.getItem('bioascension_analysis');
    if (storedAnalysis) {
      setAnalysis(storedAnalysis);
    }
  }, []);

  const reportPrice = '$1.99';

  const handlePayment = async () => {
    if (!email || !name) {
      alert('Please fill in your email and name');
      return;
    }

    setIsProcessing(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // In a real app, you would integrate with <PERSON><PERSON> or another payment processor
      console.log('Processing payment for genetic analysis report');
      
      // Send email with analysis
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name,
          analysis,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send email');
      }

      const result = await response.json();
      console.log('Email sent successfully:', result);

      // Store email for success page
      localStorage.setItem('bioascension_email', email);
      
      // Redirect to success page
      router.push('/success');
    } catch (error) {
      console.error('Payment/Email error:', error);
      alert('Payment successful but email delivery failed. Please contact support.');
      router.push('/success');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-gradient-to-br from-deepblue via-teal to-lightblue min-h-screen">
      <Head>
        <title>Complete Your Purchase - GenoBlueprint</title>
        <meta name="description" content="Complete your purchase to receive your personalized genetic potential report." />
      </Head>

      {/* Header */}
      <header className="bg-deepblue/80 backdrop-blur-sm text-white shadow-lg">
        <div className="max-w-6xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="font-extrabold text-2xl tracking-tight">
            <span className="bg-gradient-to-r from-teal to-white text-transparent bg-clip-text">GenoBlueprint</span>
          </div>
          <button
            onClick={() => router.push('/quiz')}
            className="text-teal hover:text-white transition-all duration-300 flex items-center space-x-2"
          >
            <span>←</span>
            <span>Back to Quiz</span>
          </button>
        </div>
      </header>

      {/* Payment Content */}
      <div className="max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="relative mb-8">
            <div className="w-24 h-24 mx-auto relative">
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-teal to-white opacity-20 animate-pulse"></div>
              <div className="absolute inset-2 rounded-full bg-gradient-to-r from-teal to-white opacity-40 animate-ping"></div>
              <div className="absolute inset-4 rounded-full bg-gradient-to-r from-teal to-white flex items-center justify-center animate-spin">
                <span className="text-3xl text-white">🧬</span>
              </div>
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">Your Analysis is Ready!</h1>
          <p className="text-xl text-white/90 mb-2">Complete your purchase to unlock your personalized genetic report</p>
          <div className="flex items-center justify-center space-x-6 text-white/80 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-green-400">✓</span>
              <span>95% Accuracy</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-400">✓</span>
              <span>10,000+ Users Tested</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-400">✓</span>
              <span>Instant Delivery</span>
            </div>
          </div>
        </div>

        {/* Pricing Card */}
        <div className="max-w-md mx-auto mb-12">
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border-2 border-teal/20">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-teal to-deepblue rounded-full mb-4">
                <span className="text-2xl text-white">📊</span>
              </div>
              <h3 className="text-2xl font-bold text-deepblue mb-2">Complete Genetic Analysis</h3>
              <div className="text-4xl font-bold text-deepblue mb-2">$1.99</div>
              <p className="text-gray-600">One-time payment • Instant access</p>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700">Complete height prediction analysis</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700">Puberty stage assessment</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700">Facial development timeline</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700">Personalized optimization strategies</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700">Sent instantly to your email</span>
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 border border-teal/20 rounded-xl p-4">
              <div className="flex items-center justify-center space-x-2 text-teal font-semibold">
                <span>🔥</span>
                <span>Limited Time Offer</span>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-deepblue mb-2">Secure Checkout</h2>
              <p className="text-gray-600">Complete your purchase to receive your personalized report</p>
            </div>

            <div className="space-y-6">
              {/* Contact Information */}
              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-deepblue mb-4 flex items-center">
                  <span className="mr-2">📧</span>
                  Contact Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-deepblue mb-2">Email Address</label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal focus:border-transparent transition-all duration-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-deepblue mb-2">Full Name</label>
                    <input
                      type="text"
                      placeholder="John Doe"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal focus:border-transparent transition-all duration-300"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Payment Information */}
              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-deepblue mb-4 flex items-center">
                  <span className="mr-2">💳</span>
                  Payment Information
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-deepblue mb-2">Card Number</label>
                    <input
                      type="text"
                      placeholder="1234 5678 9012 3456"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal focus:border-transparent transition-all duration-300"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-deepblue mb-2">Expiry Date</label>
                      <input
                        type="text"
                        placeholder="MM/YY"
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-deepblue mb-2">CVC</label>
                      <input
                        type="text"
                        placeholder="123"
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Stripe Logo and Security */}
              <div className="flex items-center justify-center space-x-6 py-4">
                <div className="flex items-center space-x-2 text-gray-600">
                  <span className="text-xl">🔒</span>
                  <span className="text-sm font-medium">Secured by Stripe</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <span className="text-xl">⚡</span>
                  <span className="text-sm font-medium">Instant Delivery</span>
                </div>
              </div>

              {/* Payment Button */}
              <button
                onClick={handlePayment}
                disabled={isProcessing}
                className="w-full py-4 bg-gradient-to-r from-teal to-deepblue text-white rounded-2xl hover:from-teal-400 hover:to-deepblue transition-all duration-300 disabled:opacity-50 font-bold text-lg shadow-lg transform hover:scale-105"
              >
                {isProcessing ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing Payment...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <span>Complete Purchase - $1.99</span>
                    <span>→</span>
                  </div>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="text-center mt-12">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 max-w-4xl mx-auto">
            <h3 className="text-lg font-semibold text-deepblue mb-4">Why Choose GenoBlueprint?</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-green-600 text-xl">🔒</span>
                </div>
                <h4 className="font-semibold text-deepblue mb-1">Secure Payment</h4>
                <p className="text-sm text-gray-600 text-center">SSL encrypted & Stripe protected</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-blue-600 text-xl">⚡</span>
                </div>
                <h4 className="font-semibold text-deepblue mb-1">Instant Delivery</h4>
                <p className="text-sm text-gray-600 text-center">Report sent immediately to email</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-purple-600 text-xl">🎯</span>
                </div>
                <h4 className="font-semibold text-deepblue mb-1">95% Accuracy</h4>
                <p className="text-sm text-gray-600 text-center">Validated by 10,000+ users</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-teal-600 text-xl">🛡️</span>
                </div>
                <h4 className="font-semibold text-deepblue mb-1">Privacy First</h4>
                <p className="text-sm text-gray-600 text-center">Data never shared or sold</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 max-w-md mx-auto">
            <p className="text-xs text-gray-600">
              Your data is processed securely and will never be shared with third parties.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
