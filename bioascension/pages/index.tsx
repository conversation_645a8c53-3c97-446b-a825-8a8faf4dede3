import Head from 'next/head';
import { useState } from 'react';

export default function Home() {
  const [openFaq, setOpenFaq] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const faqs = [
    {
      question: 'How accurate is your height prediction?',
      answer: 'Our predictions are based on a combination of genetic patterns, growth data, and AI modeling. While nothing is 100% precise, our system typically provides a highly accurate range of 95%, especially if you\'re still in puberty (ages 12–25).'
    },
    {
      question: 'Why not just get a DNA test and an X-ray of my growth plates instead?',
      answer: 'DNA tests take 2–6 weeks and can cost up to $300. A growth plate X-ray and height prediction from an endocrinologist can run over $500. We deliver both insights with 95% accuracy—instantly—by analyzing hundreds of millions of data points.'
    },
    {
      question: 'What data do you use to make predictions?',
      answer: 'We analyze your age, sex, current height, growth history, parental heights, and facial structure to model your growth trajectory. Your genetic background is also factored in to predict your height and development with top-tier accuracy.'
    },
    {
      question: 'Can I still grow if my parents are short?',
      answer: 'Yes. While genetics play a large role, some individuals surpass their parents due to skipped-generation traits or optimized puberty environments.'
    },
    {
      question: 'Does late puberty mean I\'ll grow more?',
      answer: 'Often, yes. Late bloomers tend to grow later and sometimes for longer. It can be a good sign if you\'re still developing at 16–17.'
    },
    {
      question: 'How long do I have left in puberty?',
      answer: 'It varies. Most males finish puberty between 17–20. Our tool estimates your remaining puberty time based on your current stage.'
    },
    {
      question: 'How do I know if my growth plates are still open?',
      answer: 'Our systems estimate this based on your growth history, puberty markers, and genetic background.'
    },
    {
      question: 'What\'s the best age to use this tool for accurate predictions?',
      answer: 'Ages 12–20 are ideal. The earlier you start, the more actionable insight you\'ll get to influence your growth potential.'
    },
    {
      question: 'How does your system work?',
      answer: 'Our systems compare your input data against hundreds of millions of known growth trajectories and genetic trends to predict height and facial maturity.'
    },
    {
      question: 'Is my data safe and private?',
      answer: 'Absolutely. We don\'t sell or share your data. Everything is processed securely and used only to generate your results.'
    },
    {
      question: 'How fast will I get my results?',
      answer: 'Most results are delivered instantly or within a few minutes, depending on how much data you provide.'
    },
    {
      question: 'What does "50% facial maturity" mean?',
      answer: 'It means you\'re halfway through your facial bone development. Expect more changes in jaw width, cheekbone prominence, and overall facial definition.'
    }
  ];

  return (
    <div className="bg-lightblue min-h-screen flex flex-col">
      <Head>
        <title>GenoBlueprint – Discover Your Genetic Potential</title>
        <meta name="description" content="Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of." />
      </Head>
      {/* Navbar */}
      <header className="sticky top-0 z-30 bg-deepblue text-white shadow flex items-center justify-between px-8 py-4">
        <a href="#" className="font-extrabold text-2xl tracking-tight flex items-center gap-2 hover:scale-105 transition-transform duration-200">
          <span className="bg-gradient-to-r from-teal to-deepblue text-transparent bg-clip-text">GenoBlueprint</span>
        </a>
        <nav className="hidden md:flex gap-10 text-base font-semibold mx-auto">
          <a href="#how" className="hover:text-teal transition">How It Works</a>
          <a href="#pricing" className="hover:text-teal transition">Pricing</a>
          <a href="#faq" className="hover:text-teal transition">FAQ</a>
          <a href="/contact-us" className="hover:text-teal transition">Contact</a>
          <a href="/refund-policy" className="hover:text-teal transition">Refund Policy</a>
        </nav>
        <a href="/quiz" className="bg-gradient-to-r from-teal to-deepblue text-white px-6 py-2 rounded-lg shadow font-bold hover:from-teal-400 hover:to-deepblue transition">Start Quiz</a>
      </header>

      {/* Hero Section */}
      <section className="flex justify-center items-center min-h-[80vh] p-4 sm:p-6 md:p-12 bg-white">
        <div className="w-full max-w-6xl flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12">
          <div className="flex-1 flex flex-col justify-center items-center md:items-start">
            <div className="mb-4 md:mb-6">
              <span className="inline-block bg-teal/10 text-teal px-3 py-1 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-semibold">
                🧬 Science-Based Analysis
              </span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 md:mb-6 leading-tight text-deepblue text-center md:text-left">
              Discover & Maximize Your <span className="text-teal">Genetic Potential</span>
            </h1>
            <p className="text-lg md:text-xl mb-6 md:mb-8 max-w-lg text-gray-600 leading-relaxed text-center md:text-left">
              Looksmax your face, height, frame by understanding what your genetics and puberty are truly capable of.
            </p>
            <div className="mb-6 md:mb-8">
              <a href="/quiz" className="inline-flex items-center justify-center bg-gradient-to-r from-teal to-deepblue text-white px-6 py-3 md:px-8 md:py-4 rounded-xl shadow-lg text-base md:text-lg font-bold hover:shadow-xl hover:scale-105 transition-all duration-300">
                Start Your Analysis
                <span className="ml-2">→</span>
              </a>
            </div>
            <div className="flex items-center text-xs sm:text-sm text-gray-500">
              <span className="mr-4">✓ 95% Accuracy</span>
              <span>✓ Instant Results</span>
            </div>
          </div>
          <div className="flex-1 flex justify-center items-center w-full max-w-[300px] md:max-w-none mx-auto">
            {/* DNA visualization component - adjust size for mobile */}
            <div className="relative w-full max-w-[280px] md:max-w-none">
              {/* Main DNA Analysis Container - reduce size on mobile */}
              <div className="w-64 h-64 md:w-96 md:h-96 relative flex items-center justify-center">
                {/* Charming background elements */}
                <div className="absolute inset-0 bg-gradient-to-br from-teal/5 via-transparent to-deepblue/5 rounded-full"></div>

                {/* Elegant scanning rings */}
                <div className="absolute inset-0 rounded-full border-2 border-teal/30 animate-spin shadow-lg" style={{ animationDuration: '25s' }}></div>
                <div className="absolute inset-8 rounded-full border border-deepblue/25 animate-spin shadow-md" style={{ animationDuration: '20s', animationDirection: 'reverse' }}></div>
                <div className="absolute inset-16 rounded-full border border-teal/15 animate-spin" style={{ animationDuration: '15s' }}></div>

                {/* Magical sparkle effects */}
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-teal rounded-full animate-ping opacity-40"
                    style={{
                      top: `${20 + Math.sin(i * 45 * Math.PI / 180) * 30}%`,
                      left: `${50 + Math.cos(i * 45 * Math.PI / 180) * 30}%`,
                      animationDelay: `${i * 0.8}s`,
                      animationDuration: '3s'
                    }}
                  ></div>
                ))}

                {/* Central DNA Helix */}
                <div className="relative w-64 h-64 flex items-center justify-center">
                  {/* Beautiful DNA Double Helix Structure */}
                  <div className="relative w-52 h-52 dna-helix">
                    {/* Create the elegant double helix */}
                    {[...Array(20)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute inset-0"
                        style={{
                          transform: `rotateZ(${i * 18}deg)`,
                          opacity: 0.7 + (i % 3) * 0.1
                        }}
                      >
                        {/* Left DNA strand with glow */}
                        <div className="absolute left-1/3 top-0 w-1.5 h-full bg-gradient-to-b from-teal via-teal/90 to-teal/50 rounded-full transform -rotate-15 shadow-lg shadow-teal/30"></div>
                        {/* Right DNA strand with glow */}
                        <div className="absolute right-1/3 top-0 w-1.5 h-full bg-gradient-to-b from-deepblue via-deepblue/90 to-deepblue/50 rounded-full transform rotate-15 shadow-lg shadow-deepblue/30"></div>

                        {/* Charming base pairs with subtle animation */}
                        {[...Array(10)].map((_, pair) => (
                          <div
                            key={pair}
                            className="absolute left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-teal via-white to-deepblue rounded-full shadow-sm"
                            style={{
                              top: `${8 + pair * 8}%`,
                              opacity: 0.8 + Math.sin(pair * 0.5) * 0.2,
                              transform: `rotateZ(${pair * 12}deg) scale(${0.8 + Math.sin(pair * 0.3) * 0.2})`
                            }}
                          ></div>
                        ))}
                      </div>
                    ))}

                    {/* Enchanting central core */}
                    <div className="absolute inset-1/4 bg-gradient-to-br from-teal/30 to-deepblue/30 rounded-full backdrop-blur-sm border-2 border-white/40 shadow-2xl glow-effect"></div>
                    <div className="absolute inset-1/3 bg-gradient-to-br from-teal to-deepblue rounded-full shadow-xl animate-pulse flex items-center justify-center border-2 border-white/20">
                      <span className="text-white text-3xl animate-bounce" style={{ animationDuration: '2s' }}>🧬</span>
                    </div>
                  </div>

                  {/* Gentle scanning beam effect */}
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-0 left-1/2 w-1 h-full bg-gradient-to-b from-transparent via-teal/60 to-transparent opacity-50 animate-spin shadow-lg shadow-teal/20" style={{ animationDuration: '4s' }}></div>
                  </div>
                </div>

                {/* Credibility Indicators Outside Circle */}
                <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 flex space-x-4">
                  <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg p-3 border border-teal/20">
                    <div className="text-center">
                      <div className="text-xl font-bold text-teal">95%</div>
                      <div className="text-xs text-gray-600 font-medium">Accuracy</div>
                    </div>
                  </div>

                  <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg p-3 border border-deepblue/20">
                    <div className="text-center">
                      <div className="text-xl font-bold text-deepblue">10K+</div>
                      <div className="text-xs text-gray-600 font-medium">Users Tested</div>
                    </div>
                  </div>
                </div>

                <div className="absolute top-16 -left-12 floating-element" style={{ animationDelay: '2s' }}>
                  <div className="bg-gradient-to-br from-teal to-deepblue rounded-2xl shadow-xl p-4 border border-white/30 hover:scale-105 transition-all duration-300">
                    <div className="text-center">
                      <div className="text-white text-2xl mb-2">📏</div>
                      <div className="text-white text-sm font-medium">Height</div>
                      <div className="text-white/80 text-xs">Prediction</div>
                    </div>
                  </div>
                </div>

                <div className="absolute bottom-16 -right-12 floating-element" style={{ animationDelay: '1.5s' }}>
                  <div className="bg-gradient-to-br from-deepblue to-teal rounded-2xl shadow-xl p-4 border border-white/30 hover:scale-105 transition-all duration-300">
                    <div className="text-center">
                      <div className="text-white text-2xl mb-2">🧬</div>
                      <div className="text-white text-sm font-medium">Growth</div>
                      <div className="text-white/80 text-xs">Timeline</div>
                    </div>
                  </div>
                </div>

                {/* Delightful floating genetic particles */}
                {[...Array(15)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-gradient-to-br from-teal to-deepblue rounded-full shadow-lg particle-orbit opacity-70 animate-pulse"
                    style={{
                      top: '50%',
                      left: '50%',
                      animationDelay: `${i * 0.4}s`,
                      animationDuration: `${10 + i * 0.5}s`
                    }}
                  ></div>
                ))}

                {/* Charming scientific indicators */}
                <div className="absolute top-6 left-6 floating-element" style={{ animationDelay: '3s' }}>
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg px-3 py-2 border border-teal/20">
                    <div className="text-xs text-teal font-bold flex items-center">
                      <span className="w-2 h-2 bg-teal rounded-full mr-2 animate-pulse"></span>
                      DNA Analysis Active
                    </div>
                  </div>
                </div>

                <div className="absolute bottom-6 right-6 floating-element" style={{ animationDelay: '4s' }}>
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg px-3 py-2 border border-deepblue/20">
                    <div className="text-xs text-deepblue font-bold flex items-center">
                      <span className="w-2 h-2 bg-deepblue rounded-full mr-2 animate-pulse"></span>
                      Processing Complete
                    </div>
                  </div>
                </div>

                {/* Elegant connecting constellation */}
                <div className="absolute inset-0 pointer-events-none opacity-25">
                  <svg className="w-full h-full" viewBox="0 0 400 400">
                    <circle
                      cx="200"
                      cy="200"
                      r="160"
                      stroke="url(#charmingGradient)"
                      strokeWidth="2"
                      fill="none"
                      strokeDasharray="15,10"
                      className="animate-spin"
                      style={{ animationDuration: '30s' }}
                    />
                    <circle
                      cx="200"
                      cy="200"
                      r="120"
                      stroke="url(#charmingGradient2)"
                      strokeWidth="1"
                      fill="none"
                      strokeDasharray="8,5"
                      className="animate-spin"
                      style={{ animationDuration: '20s', animationDirection: 'reverse' }}
                    />
                    <defs>
                      <linearGradient id="charmingGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#00C2A8" stopOpacity="0.8" />
                        <stop offset="50%" stopColor="#0A0E3F" stopOpacity="0.4" />
                        <stop offset="100%" stopColor="#00C2A8" stopOpacity="0.8" />
                      </linearGradient>
                      <linearGradient id="charmingGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#0A0E3F" stopOpacity="0.6" />
                        <stop offset="50%" stopColor="#00C2A8" stopOpacity="0.3" />
                        <stop offset="100%" stopColor="#0A0E3F" stopOpacity="0.6" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sample Predictions Dashboard */}
      <section className="py-24 px-6 md:px-20 bg-gray-50" id="dashboard">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Sample Report
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              See Your <span className="text-teal">Potential</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Preview what your personalized genetic analysis report will reveal about your growth potential
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Genetic Height Growth Range */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-2xl">📏</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-deepblue">Genetic Height Growth Range</h3>
                    <p className="text-sm text-gray-600">Age: 16 | Current: 173cm (5'8")</p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  {[
                    { height: '175cm (5\'9")', probability: 95, color: 'from-green-400 to-green-600' },
                    { height: '178cm (5\'10")', probability: 65, color: 'from-teal to-teal-600' },
                    { height: '180cm (5\'11")', probability: 20, color: 'from-blue-400 to-blue-600' },
                    { height: '183cm (6\'0")', probability: 7, color: 'from-purple-400 to-purple-600' },
                    { height: '185cm (6\'1")', probability: 5, color: 'from-orange-400 to-orange-600' },
                    { height: '188cm (6\'2")', probability: 2, color: 'from-red-400 to-red-600' },
                    { height: '190cm (6\'3+)', probability: 1, color: 'from-gray-400 to-gray-600' }
                  ].map((item, idx) => (
                    <div key={idx} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">{item.height}</span>
                      <div className="flex items-center">
                        <div className="w-16 h-2 bg-gray-200 rounded-full mr-2">
                          <div
                            className={`h-full bg-gradient-to-r ${item.color} rounded-full transition-all duration-1000 ease-out`}
                            style={{ width: `${item.probability}%` }}
                          ></div>
                        </div>
                        <span className="text-xs font-bold text-gray-600 w-8 text-right">{item.probability}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Optimized Height Growth Range */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-teal to-deepblue rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-all duration-300">
                    <span className="text-2xl text-white">🚀</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-deepblue">Genetics + Optimized Height Growth Range</h3>
                    <p className="text-sm text-gray-600">Age: 16 | Current: 173cm (5'8")</p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  {[
                    { height: '175cm (5\'9")', probability: 100, color: 'from-green-400 to-green-600' },
                    { height: '178cm (5\'10")', probability: 95, color: 'from-teal to-teal-600' },
                    { height: '180cm (5\'11")', probability: 40, color: 'from-blue-400 to-blue-600' },
                    { height: '183cm (6\'0")', probability: 10, color: 'from-purple-400 to-purple-600' },
                    { height: '185cm (6\'1")', probability: 6, color: 'from-orange-400 to-orange-600' },
                    { height: '188cm (6\'2")', probability: 2, color: 'from-red-400 to-red-600' },
                    { height: '190cm (6\'3+)', probability: 1, color: 'from-gray-400 to-gray-600' }
                  ].map((item, idx) => (
                    <div key={idx} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">{item.height}</span>
                      <div className="flex items-center">
                        <div className="w-16 h-2 bg-gray-200 rounded-full mr-2">
                          <div
                            className={`h-full bg-gradient-to-r ${item.color} rounded-full transition-all duration-1000 ease-out`}
                            style={{ width: `${item.probability}%` }}
                          ></div>
                        </div>
                        <span className="text-xs font-bold text-gray-600 w-8 text-right">{item.probability}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Hormone Deployment */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-2xl">🧪</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-deepblue">Hormone Deployment</h3>
                    <p className="text-sm text-gray-600">Active development phase</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 rounded-xl p-3 border-l-4 border-teal">
                    <p className="text-sm text-gray-700 font-medium">Shoulders will widen by 2–3 inches.</p>
                  </div>
                  <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 rounded-xl p-3 border-l-4 border-teal">
                    <p className="text-sm text-gray-700 font-medium">Facial bones are thickening due to increased testosterone levels.</p>
                  </div>
                  <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 rounded-xl p-3 border-l-4 border-teal">
                    <p className="text-sm text-gray-700 font-medium">Facial growth plates are beginning to slow down, reducing the rate of forward and width-based facial development.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Facial Maturity */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-2xl">💀</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-deepblue">Facial Maturity</h3>
                    <p className="text-sm text-gray-600">What you can expect:</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-start">
                    <span className="text-teal mr-2 mt-1">•</span>
                    <span className="text-sm text-gray-700">Jaw is beginning to widen</span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-teal mr-2 mt-1">•</span>
                    <span className="text-sm text-gray-700">Gonial angle will appear more defined</span>
                  </div>
                  <div className="flex items-start">
                    <span className="text-teal mr-2 mt-1">•</span>
                    <span className="text-sm text-gray-700">Cheekbones are expanding outward and becoming more prominent</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Puberty Stage */}
            <div className="group lg:col-span-2">
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-2xl">⏳</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-deepblue">Puberty Stage</h3>
                    <p className="text-sm text-gray-600">Growth timeline assessment</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 rounded-xl p-4 border-l-4 border-teal mb-4">
                      <p className="text-sm text-gray-700 font-medium">Approximately 10–24 months of puberty remaining</p>
                    </div>
                    <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 rounded-xl p-4 border-l-4 border-teal">
                      <p className="text-sm text-gray-700 font-medium">Growth plates are 70% fused.</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="relative">
                      <div className="w-32 h-32 rounded-full border-8 border-gray-200 flex items-center justify-center">
                        <div className="w-24 h-24 rounded-full border-8 border-transparent border-t-teal border-r-deepblue flex items-center justify-center">
                          <span className="text-2xl font-bold text-deepblue">70%</span>
                        </div>
                      </div>
                      <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-teal border-r-deepblue" style={{ transform: 'rotate(252deg)' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional info */}
          <div className="bg-white rounded-2xl p-8 max-w-4xl mx-auto border border-gray-200 shadow-lg">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-deepblue mb-6">Your Complete Analysis Includes</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-3">
                    <span className="text-2xl">🎯</span>
                  </div>
                  <span className="font-semibold text-deepblue">Personalized Predictions</span>
                  <span className="text-sm text-gray-600 mt-1">Tailored to your genetics</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-3">
                    <span className="text-2xl">📈</span>
                  </div>
                  <span className="font-semibold text-deepblue">Puberty & Hormone Profile</span>
                  <span className="text-sm text-gray-600 mt-1">Upcoming physical and hormonal changes</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-3">
                    <span className="text-2xl">💡</span>
                  </div>
                  <span className="font-semibold text-deepblue">Optimization Tips</span>
                  <span className="text-sm text-gray-600 mt-1">Actionable recommendations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why GenoBlueprint */}
      <section className="py-16 md:py-24 px-4 sm:px-6 md:px-20 bg-gray-50" id="why">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-10 md:mb-16">
            <span className="inline-block bg-teal/10 text-teal px-3 py-1 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-semibold mb-3 md:mb-4">
              Why Choose Us
            </span>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-extrabold text-deepblue mb-4 md:mb-6">
              Why Choose <span className="text-teal">GenoBlueprint</span>?
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Your height and looks are 80% determined by genetics. The remaining 20% is shaped by your lifestyle during puberty.
            </p>
          </div>
          

          {/* Comparison Text */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-bold text-deepblue mb-3">🧬 DNA Tests:</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                    <span>🕒 2–6 weeks wait</span>
                    <span>💰 Up to $300</span>
                    <span>📦 Requires lab kit & shipping</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    <strong>✅ GenoBlueprint</strong> figures out your genetics by asking simple questions about things like your head shape, lips, nose, eyes, eyebrows, and more.
                    You can also upload a photo (if you want) to help the system double-check your features, but you don't have to. You'll still get results even without it.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-bold text-deepblue mb-3">🦴 Growth Plate X-Ray + Endocrinologist Visit:</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                    <span>🕒 1–2 weeks to schedule</span>
                    <span>💰 Over $500</span>
                    <span>🏥 In-person visit required</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    <strong>✅ Endocrinologists</strong> predict your height by taking an X-ray, checking your parents' height, and asking about your growth — similar to GenoBlueprint's quiz.
                    But they don't look at your full genetic background, which can make their prediction less accurate.
                  </p>
                </div>

                <div className="bg-teal/10 p-6 rounded-xl border border-teal/20">
                  <h4 className="font-bold text-deepblue mb-3">Case 1:</h4>
                  <p className="text-sm text-gray-700 mb-4">
                    Some people's genetics let them grow for 2–3 more years, even if their bones look 50% closed. A doctor might say "you have 1–2 years," but <strong>GenoBlueprint</strong> sees your ethnic growth pattern and knows you will grow longer.
                  </p>
                  
                  <h4 className="font-bold text-deepblue mb-3">Case 2:</h4>
                  <p className="text-sm text-gray-700">
                    Sometimes your growth plates look open, but your body is going through puberty faster on the inside. A doctor might think you have 2 years left, but <strong>GenoBlueprint</strong> spots early signs like acne or voice changes and says you might only have 6–12 months to grow. It also gives tips to help you grow for as long as possible.
                  </p>
                </div>

                <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 p-6 rounded-xl border border-teal/30">
                  <h3 className="text-lg font-bold text-deepblue mb-3">✅ GenoBlueprint:</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
                    <span>⚡ Delivered instantly</span>
                    <span>💲 Free by sharing quiz with 3 friends or a one time payment of $4.99</span>
                    <span>📩 No appointments, labs, or waiting</span>
                  </div>
                  <p className="text-sm text-gray-700">
                    <strong>✅ GenoBlueprint</strong> asks you questions about your growth, puberty, family, habits, and background.
                    It uses your quiz answers, and a photo if you want to add one, to find out how your body is likely to grow.
                    Then it gives you a super accurate prediction of your final height and puberty stage, no DNA test or doctor needed.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {[
              {
                icon: '⚡',
                title: 'Instant Results',
                desc: 'Get your comprehensive report in minutes, not weeks. No lab visits or waiting periods required.',
                highlight: 'Under 10 minutes'
              },
              {
                icon: '🔬',
                title: '95% Accuracy',
                desc: 'Built on advanced technology with hundreds of millions of data points.',
                highlight: 'Science-backed'
              },
              {
                icon: '💸',
                title: 'Affordable Access',
                desc: 'Professional-grade genetic analysis starting at just $1.99. No expensive lab fees.',
                highlight: 'From $1.99'
              },
              {
                icon: '🧑‍🔬',
                title: 'Looksmax Optimized',
                desc: 'Specifically designed for teens and young adults focused on maximizing their genetic potential.',
                highlight: 'Ages 12-25'
              },
            ].map((item, i) => (
              <div key={i} className="group relative h-full">
                <div className="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                  {/* Icon with background */}
                  <div className="w-16 h-16 bg-teal/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-3xl">{item.icon}</span>
                  </div>

                  <h3 className="text-xl font-bold mb-3 text-deepblue text-center">{item.title}</h3>

                  {/* Highlight badge */}
                  <div className="bg-teal/10 text-teal px-3 py-1 rounded-full text-sm font-semibold mb-4 border border-teal/20">
                    {item.highlight}
                  </div>

                  <p className="text-gray-600 text-center leading-relaxed text-sm flex-grow">{item.desc}</p>

                  {/* Bottom accent */}
                  <div className="absolute bottom-0 left-8 right-8 h-1 bg-gradient-to-r from-teal to-deepblue rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 px-6 md:px-20 bg-white" id="how">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Our Process
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              How It <span className="text-teal">Works</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our system analyzes your quiz responses using a scientifically backed process to reveal your growth and development potential
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                icon: '📝',
                title: 'Complete the Quiz',
                desc: ' Answer 75+ in-depth questions about your genetics, growth patterns, and physical development history.',
                time: '⏱️ Takes 15–20 minutes',
                step: '01'
              },
              {
                icon: '📊',
                title: 'Growth Pattern Detection',
                desc: 'Our system analyzes your growth pattern, growth rate, and recurring trends in your genetic background. By mapping your genetic tree, we compare your development to others with similar genetic profiles, allowing us to predict your final height with top-tier accuracy.',
                time: '⚙️ Accurate Growth Projection',
                step: '02'
              },
              {
                icon: '⚡',
                title: 'Get Your Personalized Report',
                desc: ' Receive your detailed growth and height prediction report directly in your gmail inbox, along with tips to help you stay in your growth window longer and reach your full potential.',
                time: '📩 Instant Delivery',
                step: '03'
              },
            ].map((item, i) => (
              <div key={i} className="relative group">
                {/* Step connector line */}
                {i < 2 && (
                  <div className="hidden md:block absolute top-20 left-full w-8 h-0.5 bg-gray-300 z-0">
                    <div className="h-full bg-gradient-to-r from-teal to-deepblue rounded-full"></div>
                  </div>
                )}

                <div className="relative z-10 bg-white rounded-2xl shadow-lg p-8 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                  {/* Step number */}
                  <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-teal to-deepblue rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    {item.step}
                  </div>

                  {/* Icon */}
                  <div className="w-16 h-16 bg-teal/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-3xl">{item.icon}</span>
                  </div>

                  <h3 className="text-xl font-bold mb-4 text-deepblue">{item.title}</h3>

                  {/* Time badge */}
                  <div className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium mb-4 border border-gray-200">
                    {item.time}
                  </div>

                  <p className="text-gray-600 leading-relaxed text-sm">{item.desc}</p>

                  {/* Bottom accent */}
                  <div className="absolute bottom-0 left-8 right-8 h-1 bg-gradient-to-r from-teal to-deepblue rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to action */}
          <div className="text-center mt-16">
            <a
              href="/quiz"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal to-deepblue text-white rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 text-lg font-bold"
            >
              Start Your Analysis
              <span className="ml-2">→</span>
            </a>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-24 px-6 md:px-20 bg-white" id="pricing">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Pricing Plans
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              Simple, Cheap & <span className="text-teal">Effective</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Take action during puberty - discover and unlock your full potential for just $1.99
            </p>
          </div>

          <div className="flex flex-col lg:flex-row gap-8 max-w-5xl mx-auto justify-center">
            {/* Basic Plan */}
            <div className="flex-1 relative group">
              <div className="bg-white rounded-3xl shadow-2xl p-10 flex flex-col border-2 border-gray-200 hover:border-teal hover:shadow-3xl transition-all duration-300 h-full">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-teal/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📊</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-2 text-deepblue">Essential Report</h3>
                  <div className="text-5xl font-extrabold mb-2 text-deepblue">$1.99</div>
                  <p className="text-gray-500">One-time payment</p>
                </div>

                <ul className="space-y-4 mb-8 flex-grow">
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Genetic only height prediction analysis</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700"> Genetic + Optimized height prediction analysis</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Puberty stage assessment                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Growth timeline projections</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Face maturity timeline</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">How your face and body shape will grow and mature</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Instant email delivery</span>
                  </li>
                </ul>

                <a href="/quiz" className="w-full bg-teal text-white py-4 rounded-2xl shadow-lg hover:bg-teal-600 hover:shadow-xl font-bold text-lg transition-all duration-300 group-hover:scale-105 block text-center">
                  Get Started
                </a>
              </div>
            </div>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 text-center">
            <div className="bg-gray-50 rounded-2xl p-8 border border-gray-200">
              <h3 className="text-lg font-semibold text-deepblue mb-6">Trusted & Secure</h3>
              <div className="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-12">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-white rounded-xl shadow-md flex items-center justify-center mr-3">
                    <svg className="w-8 h-4" viewBox="0 0 60 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M59.5759 13.5587C59.5759 12.8176 59.5759 12.0765 59.5759 11.3354C59.5759 10.5943 59.5759 9.85317 59.5759 9.11206C59.5759 8.37095 59.5759 7.62984 59.5759 6.88873C59.5759 6.14762 59.5759 5.40651 59.5759 4.6654C59.5759 3.92429 59.5759 3.18318 59.5759 2.44207C59.5759 1.70096 59.5759 0.959854 59.5759 0.218746H0.424072C0.424072 0.959854 0.424072 1.70096 0.424072 2.44207C0.424072 3.18318 0.424072 3.92429 0.424072 4.6654C0.424072 5.40651 0.424072 6.14762 0.424072 6.88873C0.424072 7.62984 0.424072 8.37095 0.424072 9.11206C0.424072 9.85317 0.424072 10.5943 0.424072 11.3354C0.424072 12.0765 0.424072 12.8176 0.424072 13.5587H59.5759Z" fill="#6772E5" />
                      <path d="M26.3158 9.11206C26.3158 8.37095 26.3158 7.62984 26.3158 6.88873C26.3158 6.14762 26.3158 5.40651 26.3158 4.6654C26.3158 3.92429 26.3158 3.18318 26.3158 2.44207C26.3158 1.70096 26.3158 0.959854 26.3158 0.218746H33.6842C33.6842 0.959854 33.6842 1.70096 33.6842 2.44207C33.6842 3.18318 33.6842 3.92429 33.6842 4.6654C33.6842 5.40651 33.6842 6.14762 33.6842 6.88873C33.6842 7.62984 33.6842 8.37095 33.6842 9.11206H26.3158Z" fill="white" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">Stripe</div>
                    <div className="text-sm text-gray-600">Secure payments</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-2xl">🔒</span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">SSL Encrypted</div>
                    <div className="text-sm text-gray-600">Bank-level security</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">Instant Delivery</div>
                    <div className="text-sm text-gray-600">Results in minutes</div>
                  </div>
                </div>

                  </div>
                  </div>
                </div>
              </div>
      </section>

      {/* Reviews */}
      <section className="py-24 px-6 md:px-20 bg-gray-50" id="reviews">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Customer Reviews
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              What Our <span className="text-teal">Users Say</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Real stories from teens and young adults who discovered their true growth potential
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Review 1 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400 text-xl mb-2">
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                  </div>
                </div>
                <h3 className="text-lg font-bold text-deepblue mb-2">Gave me a lot of relief</h3>
                <p className="text-sm text-gray-500 mb-4">By Jerome P.</p>

                <div className="space-y-4 text-gray-700 leading-relaxed">
                  <p className="text-sm">
                    I'm 16 and 5'2, and honestly, I've lost all hope about my height. I barely grown over the past 3 years and this year I havent grown at all. Mean while most of my friends kept growing taller. My dad is 5'8" and my older brother is 5'10", so I was starting to think I just got unlucky.
                  </p>
                  <p className="text-sm">
                    I found GenoBlueprint from tiktok and decided to try it and it actually made a lot of sense. It showed that people with my background who are Ghanaian, Akan often grow early in puberty, slow down for a while, then get a second big growth spurt around 18 or 19. That's exactly what happened to my brother and even my dad, and I never really noticed the pattern until now.
                  </p>
                  <p className="text-sm">
                    The crazy part is how accurate it predicted how I would later grow just like my dad and brother. It made me answer a bunch of questions about my past growth and compared it with data from people like me. Now I know I'm probably not done growing. This gave me a lot of hope and relief, and I actually know how to take care of myself during this stage to maximize whatever height I have left.
                  </p>
                  <p className="text-sm font-medium text-teal">
                    If you're in the same situation and feel stuck, I definitely recommend this. It's way better than guessing or stressing out for nothing.
                  </p>
                </div>
              </div>
            </div>

            {/* Review 2 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400 text-xl mb-2">
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                  </div>
                </div>
                <h3 className="text-lg font-bold text-deepblue mb-2">Super helpful 10/10</h3>
                <p className="text-sm text-gray-500 mb-4">By Kenji L.</p>

                <div className="space-y-4 text-gray-700 leading-relaxed">
                  <p className="text-sm">
                    I always think if I am done growing since I am 19 and everyone tells me to stop growing taller at 16. I used to think growth plates just closed overnight but apparently they turn out they close gradually over time within months.
                  </p>
                  <p className="text-sm">
                    The analysis said my genetic background's growth plates close during the ages 19 and 21 which makes sense for me since I am 19. I didn't know that before. Right now my growth plates 80% fused at 5'11 so there is a chance i can grow more than 6 feet.
                  </p>
                                      <p className="text-sm font-medium text-teal">
                     I started following their advice to keep my growth plates opening as long as possible. Super helpful if you ever wonder how much you are going to grow and motivates you to grow taller. Worth it for $1.99
                    </p>
                </div>
              </div>
            </div>

            {/* Review 3 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400 text-xl mb-2">
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                    <span>⭐️</span>
                  </div>
                </div>
                <h3 className="text-lg font-bold text-deepblue mb-2">Good prediction thanks</h3>
                <p className="text-sm text-gray-500 mb-4">By Ryan C.</p>

                <div className="space-y-4 text-gray-700 leading-relaxed">
                  <p className="text-sm">
                    My parents are short and my sister is 5'4 and shes done growing so I dont know if I would grow tall. The review said most brothers grow about 6 inches taller than their sister's height, and predicted I'll likely hit 5'10" with a 75% chance.
                  </p>
                  <p className="text-sm font-medium text-teal">
                    Super helpful and straight to the point.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 text-center">
            <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
              <h3 className="text-2xl font-bold text-deepblue mb-6">Join Thousands of Satisfied Users</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col items-center">
                  <div className="text-4xl font-bold text-teal mb-2">10K+</div>
                  <div className="text-gray-600">Users Analyzed</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-4xl font-bold text-teal mb-2">95%</div>
                  <div className="text-gray-600">Accuracy Rate</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-4xl font-bold text-teal mb-2">4.9/5</div>
                  <div className="text-gray-600">Average Rating</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-24 px-6 md:px-20 bg-gray-50" id="faq">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              FAQ
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              Frequently Asked <span className="text-teal">Questions</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Everything you need to know about our genetic analysis, accuracy, and how to maximize your growth potential
            </p>
          </div>

          <div className="space-y-8">
            {/* General Questions */}
          <div className="space-y-4">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-deepblue mb-2">General Questions</h3>
                <p className="text-gray-600">Essential information about our service</p>
              </div>
              {faqs.slice(0, 3).map((faq, idx) => (
              <div key={idx} className="group">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300">
                  <button
                    className="w-full text-left px-8 py-6 focus:outline-none flex justify-between items-center text-deepblue font-semibold text-lg hover:bg-gray-50 transition-all duration-300"
                    onClick={() => setOpenFaq(openFaq === idx ? null : idx)}
                    aria-expanded={openFaq === idx}
                    aria-controls={`faq-answer-${idx}`}
                  >
                    <span className="flex items-center">
                      <span className="w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                        <span className="text-teal font-bold text-sm">{String(idx + 1).padStart(2, '0')}</span>
                      </span>
                      {faq.question}
                    </span>
                    <div className={`ml-4 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center transition-all duration-300 ${openFaq === idx ? 'rotate-180 bg-teal text-white' : 'group-hover:bg-gray-200'}`}>
                      <span className="text-sm">▼</span>
                    </div>
                  </button>
                  <div
                    id={`faq-answer-${idx}`}
                    className={`overflow-hidden transition-all duration-300 ease-in-out ${openFaq === idx ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                      }`}
                  >
                    <div className="px-8 pb-6">
                      <div className="bg-gray-50 rounded-xl p-4 border-l-4 border-teal">
                        <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            </div>

            {/* Genetics & Puberty */}
            <div className="space-y-4">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-deepblue mb-2">🧬 Genetics & Puberty</h3>
                <p className="text-gray-600">Understanding your growth potential and development timeline</p>
              </div>
              {faqs.slice(3, 8).map((faq, idx) => (
                <div key={idx + 3} className="group">
                  <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300">
                    <button
                      className="w-full text-left px-8 py-6 focus:outline-none flex justify-between items-center text-deepblue font-semibold text-lg hover:bg-gray-50 transition-all duration-300"
                      onClick={() => setOpenFaq(openFaq === idx + 3 ? null : idx + 3)}
                      aria-expanded={openFaq === idx + 3}
                      aria-controls={`faq-answer-${idx + 3}`}
                    >
                      <span className="flex items-center">
                        <span className="w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                          <span className="text-teal font-bold text-sm">{String(idx + 4).padStart(2, '0')}</span>
                        </span>
                        {faq.question}
                      </span>
                      <div className={`ml-4 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center transition-all duration-300 ${openFaq === idx + 3 ? 'rotate-180 bg-teal text-white' : 'group-hover:bg-gray-200'}`}>
                        <span className="text-sm">▼</span>
                      </div>
                    </button>
                    <div
                      id={`faq-answer-${idx + 3}`}
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${openFaq === idx + 3 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                        }`}
                    >
                      <div className="px-8 pb-6">
                        <div className="bg-gray-50 rounded-xl p-4 border-l-4 border-teal">
                          <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* BioAscension Systems Questions */}
            <div className="space-y-4">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-deepblue mb-2">💻 GenoBlueprint Systems Questions</h3>
                <p className="text-gray-600">How our technology works and protects your data</p>
              </div>
              {faqs.slice(8, 12).map((faq, idx) => (
                <div key={idx + 8} className="group">
                  <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300">
                    <button
                      className="w-full text-left px-8 py-6 focus:outline-none flex justify-between items-center text-deepblue font-semibold text-lg hover:bg-gray-50 transition-all duration-300"
                      onClick={() => setOpenFaq(openFaq === idx + 8 ? null : idx + 8)}
                      aria-expanded={openFaq === idx + 8}
                      aria-controls={`faq-answer-${idx + 8}`}
                    >
                      <span className="flex items-center">
                        <span className="w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                          <span className="text-teal font-bold text-sm">{String(idx + 9).padStart(2, '0')}</span>
                        </span>
                        {faq.question}
                      </span>
                      <div className={`ml-4 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center transition-all duration-300 ${openFaq === idx + 8 ? 'rotate-180 bg-teal text-white' : 'group-hover:bg-gray-200'}`}>
                        <span className="text-sm">▼</span>
                      </div>
                    </button>
                    <div
                      id={`faq-answer-${idx + 8}`}
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${openFaq === idx + 8 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                        }`}
                    >
                      <div className="px-8 pb-6">
                        <div className="bg-gray-50 rounded-xl p-4 border-l-4 border-teal">
                          <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Safety & Data Protection */}
            <div className="space-y-4">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-deepblue mb-2">⚠️ Safety & Data Protection</h3>
                <p className="text-gray-600">We take your privacy and personal information very seriously, in full compliance with legal standards and Stripe's strict security protocols.</p>
              </div>
              {[
                {
                  question: 'Do you sell my data?',
                  answer: 'Absolutely not. We never sell or share your data in any form.'
                },
                {
                  question: 'Is my card information secure?',
                  answer: 'Yes. Your card details are fully protected and cannot be accessed or leaked. Payments are processed through Stripe.com, a leading encrypted third-party payment platform. We never see or store your card information.'
                }
              ].map((faq, idx) => (
                <div key={idx + 12} className="group">
                  <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300">
                    <button
                      className="w-full text-left px-8 py-6 focus:outline-none flex justify-between items-center text-deepblue font-semibold text-lg hover:bg-gray-50 transition-all duration-300"
                      onClick={() => setOpenFaq(openFaq === idx + 12 ? null : idx + 12)}
                      aria-expanded={openFaq === idx + 12}
                      aria-controls={`faq-answer-${idx + 12}`}
                    >
                      <span className="flex items-center">
                        <span className="w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                          <span className="text-teal font-bold text-sm">{String(idx + 13).padStart(2, '0')}</span>
                        </span>
                        {faq.question}
                      </span>
                      <div className={`ml-4 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center transition-all duration-300 ${openFaq === idx + 12 ? 'rotate-180 bg-teal text-white' : 'group-hover:bg-gray-200'}`}>
                        <span className="text-sm">▼</span>
                      </div>
                    </button>
                    <div
                      id={`faq-answer-${idx + 12}`}
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${openFaq === idx + 12 ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                        }`}
                    >
                      <div className="px-8 pb-6">
                        <div className="bg-gray-50 rounded-xl p-4 border-l-4 border-teal">
                          <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional help section */}
          <div className="mt-16 text-center" id="contact">
            <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
              <h3 className="text-2xl font-bold text-deepblue mb-4">Still have questions?</h3>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal to-deepblue text-white rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 font-bold"
              >
                <span className="mr-2">📧</span>
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-12 px-6 md:px-20 mt-auto">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <div className="font-bold text-2xl text-deepblue mb-2">GenoBlueprint</div>
              <p className="text-gray-600 text-sm">Discover and maximize your genetic potential</p>
            </div>
            <div className="flex gap-8 text-sm mb-6 md:mb-0">
              <a href="#" className="text-gray-600 hover:text-teal transition-colors duration-300">Home</a>
              <a href="#how" className="text-gray-600 hover:text-teal transition-colors duration-300">How It Works</a>
              <a href="#pricing" className="text-gray-600 hover:text-teal transition-colors duration-300">Pricing</a>
              <a href="#faq" className="text-gray-600 hover:text-teal transition-colors duration-300">FAQ</a>
              <a href="/contact-us" className="text-gray-600 hover:text-teal transition-colors duration-300">Contact</a>
              <a href="/refund-policy" className="text-gray-600 hover:text-teal transition-colors duration-300">Refund Policy</a>
            </div>
            <div className="text-xs text-gray-500">© 2025 GenoBlueprint. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  );
} 
