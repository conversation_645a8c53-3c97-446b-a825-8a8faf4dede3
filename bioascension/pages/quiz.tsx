import Head from 'next/head';
import { useState } from 'react';
import { useRouter } from 'next/router';

interface QuizData {
  // Basic Info
  biologicalSex: string;
  age: string;
  currentHeight: string;
  currentWeight: string;
  country: string;
  measurementTime: string;
  
  // Growth & Puberty Markers
  pubertyAge: string;
  recentGrowthSpurt: string;
  growthPast12Months: string;
  growingPains: string;
  handsFeetsGrowing: string;
  nightSoreness: string;
  
  // Skeletal & Facial Development
  shoeSizeIncrease: string;
  jawlineFacialMaturity: string;
  adamsApple: string;
  facialHair: string;
  cheekbones: string;
  voiceChange: string;
  bodyFrameWider: string;
  
  // Testosterone/Hormone Traits
  bodyHair: string;
  muscleGain: string;
  acne: string;
  voiceDepth: string;
  sweating: string;
  armpitGroinHair: string;
  jawNoseDefinition: string;
  
  // Genetics & Family Traits
  fatherHeight: string;
  motherHeight: string;
  hasSiblings: string;
  siblingInfo: string;
  parentsPubertyTiming: string;
  ethnicBackground: string;
  familyLateGrowth: string;
  
  // Lifestyle & Health
  exerciseFrequency: string;
  sleepHours: string;
  supplementsMedications: string;
  dairyConsumption: string;
  proteinDiet: string;
  cigaretteSmoke: string;
  soyConsumption: string;
  stressLevel: string;
  visibleAbs: string;
  
  // Psychological & Developmental Markers
  maturityComparison: string;
  stillGrowing: string;
  pubertyStage: string;
  compareToFamily: string;
  maturityVsPeers: string;
  
  // Ethnicity Background
  birthCountry: string;
  parentsCountries: string;
  grandparentsCountries: string;
  ethnicGroup: string;
  mixedEthnicity: string;
  
  // Head and Skull Shape
  headShape: string;
  headTop: string;
  headBack: string;
  forehead: string;
  headWidth: string;
  raisedLine: string;
  
  // Face Shape and Features
  faceShape: string;
  cheekbonesProminence: string;
  browBone: string;
  noseShape: string;
  nostrils: string;
  eyeSpacing: string;
  chinShape: string;
  jawlineShape: string;
  faceProjection: string;
  noseUpperLipSpace: string;
  faceGrowthDirection: string;
  
  // Eyes and Eyebrows
  eyeColor: string;
  eyelidFold: string;
  eyeDepth: string;
  eyelidType: string;
  eyebrowShape: string;
  eyebrowEyeSpace: string;
  
  // Final Consent
  email: string;
  consent: boolean;
}

const quizSections = [
  {
    title: "📋 Basic Info",
    questions: [
      { key: 'biologicalSex', question: 'What is your biological sex?', type: 'radio', options: ['Male', 'Female'] },
      { key: 'age', question: 'How old are you? (Exact age in years and months)', type: 'text', placeholder: 'e.g., 16 years 3 months' },
      { key: 'currentHeight', question: 'What is your current height?', type: 'text', placeholder: 'e.g., 5\'8" or 173cm' },
      { key: 'currentWeight', question: 'What is your current weight?', type: 'text', placeholder: 'e.g., 150 lbs or 68 kg' },
      { key: 'country', question: 'What country are you currently living in?', type: 'text', placeholder: 'e.g., United States' },
      { key: 'measurementTime', question: 'What time of day do you usually measure your height?', type: 'radio', options: ['Morning', 'Night', 'Mixed'] }
    ]
  },
  {
    title: "🔬 Growth & Puberty Markers",
    questions: [
      { key: 'pubertyAge', question: 'At what age did you notice signs of puberty beginning?', type: 'text', placeholder: 'e.g., 12 years old' },
      { key: 'recentGrowthSpurt', question: 'Have you had a recent growth spurt?', type: 'radio', options: ['Yes', 'No', 'Not sure'] },
      { key: 'growthPast12Months', question: 'How much have you grown in the past 12 months?', type: 'text', placeholder: 'e.g., 2 inches or 5 cm' },
      { key: 'growingPains', question: 'Do you experience growing pains in legs or knees?', type: 'radio', options: ['Often', 'Sometimes', 'Rarely', 'Never'] },
      { key: 'handsFeetsGrowing', question: 'Are your hands or feet still growing in size?', type: 'radio', options: ['Yes', 'No', 'Not sure'] },
      { key: 'nightSoreness', question: 'Do you feel soreness or dull pain near knees, ankles, or wrists at night?', type: 'radio', options: ['Yes', 'No', 'Sometimes'] }
    ]
  },
  {
    title: "🦴 Skeletal & Facial Development",
    questions: [
      { key: 'shoeSizeIncrease', question: 'Has your shoe size increased in the past year?', type: 'radio', options: ['Yes', 'No'] },
      { key: 'jawlineFacialMaturity', question: 'Has your jawline or facial structure noticeably matured recently?', type: 'radio', options: ['Yes', 'No', 'Slightly'] },
      { key: 'adamsApple', question: 'Do you have noticeable Adam\'s apple growth?', type: 'radio', options: ['Yes', 'No', 'Not sure'] },
      { key: 'facialHair', question: 'Do you have facial hair?', type: 'radio', options: ['None', 'Light peach fuzz', 'Full beard/mustache growth'] },
      { key: 'cheekbones', question: 'Have your cheekbones become more prominent recently?', type: 'radio', options: ['Yes', 'No', 'Slightly'] },
      { key: 'voiceChange', question: 'Has your voice changed in pitch or cracked more often recently?', type: 'radio', options: ['Yes', 'No', 'Sometimes'] },
      { key: 'bodyFrameWider', question: 'Has your overall body frame become wider (shoulders, chest)?', type: 'radio', options: ['Yes', 'No', 'Slightly'] }
    ]
  },
  {
    title: "🧬 Testosterone/Hormone Traits",
    questions: [
      { key: 'bodyHair', question: 'How would you describe your body hair?', type: 'radio', options: ['Minimal', 'Moderate', 'Dense'] },
      { key: 'muscleGain', question: 'Do you gain muscle easily with workouts?', type: 'radio', options: ['Yes', 'No', 'Somewhat', 'Unsure', 'Never tried'] },
      { key: 'acne', question: 'Do you have acne or oily skin?', type: 'radio', options: ['Mild', 'Moderate', 'Severe', 'None'] },
      { key: 'voiceDepth', question: 'How deep is your voice?', type: 'radio', options: ['High', 'Medium', 'Deep'] },
      { key: 'sweating', question: 'Do you sweat more than you did 1–2 years ago?', type: 'radio', options: ['Yes', 'No', 'Slightly'] },
      { key: 'armpitGroinHair', question: 'Are your armpits or groin hair increasing rapidly?', type: 'radio', options: ['Yes', 'No', 'Somewhat'] },
      { key: 'jawNoseDefinition', question: 'Have you noticed changes in jaw or nose definition in the past year?', type: 'radio', options: ['Yes', 'No', 'Slightly'] }
    ]
  },
  {
    title: "⚖️ Genetics & Family Traits",
    questions: [
      { key: 'fatherHeight', question: 'What is your father\'s height?', type: 'text', placeholder: 'e.g., 6\'0" or 183cm' },
      { key: 'motherHeight', question: 'What is your mother\'s height?', type: 'text', placeholder: 'e.g., 5\'6" or 168cm' },
      { key: 'hasSiblings', question: 'Do you have siblings?', type: 'radio', options: ['Yes', 'No'] },
      { key: 'siblingInfo', question: 'If yes, what is their gender, age, height?', type: 'text', placeholder: 'e.g., Brother, 18, 5\'10"' },
      { key: 'parentsPubertyTiming', question: 'Did your parents hit puberty early, average, or late?', type: 'radio', options: ['Early', 'Average', 'Late', 'Don\'t know'] },
      { key: 'ethnicBackground', question: 'What ethnic background are you from (or mix of)?', type: 'text', placeholder: 'e.g., European, Asian, African, etc.' },
      { key: 'familyLateGrowth', question: 'Has anyone in your family had late growth spurts (after age 17)?', type: 'radio', options: ['Yes', 'No', 'Don\'t know'] }
    ]
  },
  {
    title: "💪 Lifestyle & Health",
    questions: [
      { key: 'exerciseFrequency', question: 'How often do you exercise per week?', type: 'radio', options: ['0', '1–2', '3–5', '6+ times'] },
      { key: 'sleepHours', question: 'How many hours of sleep do you get per night?', type: 'radio', options: ['Less than 6', '6–8', '8+'] },
      { key: 'supplementsMedications', question: 'Do you currently take any supplements or medications?', type: 'radio', options: ['Yes', 'No'] },
      { key: 'dairyConsumption', question: 'Do you drink milk or consume dairy regularly?', type: 'radio', options: ['Yes', 'No'] },
      { key: 'proteinDiet', question: 'Do you eat a protein-rich diet?', type: 'radio', options: ['Yes', 'No', 'Sometimes'] },
      { key: 'cigaretteSmoke', question: 'Are you around cigarette smoke regularly?', type: 'radio', options: ['Yes', 'No'] },
      { key: 'soyConsumption', question: 'How often do you eat soy sauce or soy-based foods?', type: 'radio', options: ['Often', 'Sometimes', 'Rarely', 'Never'] },
      { key: 'stressLevel', question: 'Are you currently experiencing a high-stress lifestyle?', type: 'radio', options: ['Yes', 'No', 'Somewhat'] },
      { key: 'visibleAbs', question: 'Do you have visible abs?', type: 'radio', options: ['Yes', 'No'] }
    ]
  },
  {
    title: "🧠 Psychological & Developmental Markers",
    questions: [
      { key: 'maturityComparison', question: 'Do you feel more mature or taller than most of your peers?', type: 'radio', options: ['Yes', 'No', 'Same as others'] },
      { key: 'stillGrowing', question: 'Are you still noticeably growing taller month to month?', type: 'radio', options: ['Yes', 'No', 'Not sure'] },
      { key: 'pubertyStage', question: 'Do you feel like your puberty is mostly finished, midway, or barely started?', type: 'radio', options: ['Mostly finished', 'Midway', 'Barely started'] },
      { key: 'compareToFamily', question: 'Do you often compare your features to older siblings or parents?', type: 'radio', options: ['Yes', 'No', 'Sometimes'] },
      { key: 'maturityVsPeers', question: 'Are your voice, feet, or facial structure more mature than your friends?', type: 'radio', options: ['Yes', 'No', 'About the same'] }
    ]
  },
  {
    title: "📋 Ethnicity Background",
    description: "Your ethnic background plays a key role in predicting height. Genetics determine how many growth spurts you'll have, how long you'll grow, when your growth plates will close, and more.",
    questions: [
      { key: 'birthCountry', question: 'What country were you born in?', type: 'text', placeholder: 'e.g., United States' },
      { key: 'parentsCountries', question: 'What countries are your parents from?', type: 'text', placeholder: 'e.g., Mother: China, Father: Germany' },
      { key: 'grandparentsCountries', question: 'Where were your grandparents born?', type: 'text', placeholder: 'e.g., All from Italy' },
      { key: 'ethnicGroup', question: 'What ethnic group is your family part of?', type: 'text', placeholder: 'e.g., Chinese, Indian, Arab, African, European, etc.' },
      { key: 'mixedEthnicity', question: 'Do you know if your family is mixed with more than one ethnicity?', type: 'radio', options: ['Yes', 'No', 'Not sure'] }
    ]
  },
  {
    title: "🧠 Head and Skull Shape",
    questions: [
      { key: 'headShape', question: 'Is your head more round, long, or somewhere in between?', type: 'radio', options: ['Round', 'Long', 'In between'] },
      { key: 'headTop', question: 'Is the top of your head flat, rounded, or high?', type: 'radio', options: ['Flat', 'Rounded', 'High'] },
      { key: 'headBack', question: 'Does the back of your head stick out a lot or is it flat?', type: 'radio', options: ['Sticks out', 'Flat', 'In between'] },
      { key: 'forehead', question: 'Is your forehead straight up, curved back, or kind of slanted?', type: 'radio', options: ['Straight up', 'Curved back', 'Slanted'] },
      { key: 'headWidth', question: 'Is your head wider at the top or at the jaw?', type: 'radio', options: ['Wider at top', 'Wider at jaw', 'About the same'] },
      { key: 'raisedLine', question: 'Do you feel a raised line running down the top of your head?', type: 'radio', options: ['Yes', 'No', 'Not sure'] }
    ]
  },
  {
    title: "👃 Face Shape and Features",
    questions: [
      { key: 'faceShape', question: 'Is your face more long and narrow, or short and wide?', type: 'radio', options: ['Long and narrow', 'Short and wide', 'In between'] },
      { key: 'cheekbonesProminence', question: 'Do your cheekbones stick out, or are they flat?', type: 'radio', options: ['Stick out', 'Flat', 'Moderate'] },
      { key: 'browBone', question: 'Do you have a strong bone above your eyebrows (brow bone)?', type: 'radio', options: ['Strong', 'Moderate', 'Weak'] },
      { key: 'noseShape', question: 'Is your nose flat and wide, medium, or tall and narrow?', type: 'radio', options: ['Flat and wide', 'Medium', 'Tall and narrow'] },
      { key: 'nostrils', question: 'Are your nostrils round or more like slits?', type: 'radio', options: ['Round', 'Slits', 'In between'] },
      { key: 'eyeSpacing', question: 'Is there a lot of space between your eyes or just a little?', type: 'radio', options: ['A lot', 'A little', 'Normal'] },
      { key: 'chinShape', question: 'Is your chin pointy, round, or big and square?', type: 'radio', options: ['Pointy', 'Round', 'Big and square'] },
      { key: 'jawlineShape', question: 'Is your jawline sharp, curved, or soft-looking?', type: 'radio', options: ['Sharp', 'Curved', 'Soft-looking'] },
      { key: 'faceProjection', question: 'Does your face stick out in the middle (like your nose area) or is it more flat?', type: 'radio', options: ['Sticks out', 'Flat', 'In between'] },
      { key: 'noseUpperLipSpace', question: 'Is the area between your nose and upper lip long or short?', type: 'radio', options: ['Long', 'Short', 'Medium'] },
      { key: 'faceGrowthDirection', question: 'Does your face grow more forward or downward?', type: 'radio', options: ['Forward', 'Downward', 'Both equally'] }
    ]
  },
  {
    title: "👁️ Eyes and Eyebrows",
    questions: [
      { key: 'eyeColor', question: 'What color are your eyes naturally?', type: 'text', placeholder: 'e.g., Brown, Blue, Green, Hazel' },
      { key: 'eyelidFold', question: 'Do you have an inner eyelid fold (like a monolid)?', type: 'radio', options: ['Yes', 'No', 'Partial'] },
      { key: 'eyeDepth', question: 'Are your eyes deep-set or do they stick out more?', type: 'radio', options: ['Deep-set', 'Stick out', 'Normal'] },
      { key: 'eyelidType', question: 'Do you have double eyelids, single eyelids, or something in between?', type: 'radio', options: ['Double', 'Single', 'In between'] },
      { key: 'eyebrowShape', question: 'Are your eyebrows straight, arched, or angled?', type: 'radio', options: ['Straight', 'Arched', 'Angled'] },
      { key: 'eyebrowEyeSpace', question: 'How much space is there between your eyebrows and your eyes?', type: 'radio', options: ['A lot', 'Normal', 'Very little'] }
    ]
  }
];

export default function Quiz() {
  const router = useRouter();
  const [currentSection, setCurrentSection] = useState(0);
  const [quizData, setQuizData] = useState<QuizData>({} as QuizData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (key: string, value: string | boolean) => {
    setQuizData(prev => ({ ...prev, [key]: value }));
  };

  const handleNext = () => {
    if (currentSection < quizSections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // Call the AI analysis API
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ quizData }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze quiz data');
      }

      const { analysis } = await response.json();
      
      // Store the analysis in localStorage for the preparing page
      localStorage.setItem('bioascension_analysis', analysis);
      localStorage.setItem('bioascension_quiz_data', JSON.stringify(quizData));

      // Redirect to preparing results page
      router.push('/preparing');
    } catch (error) {
      console.error('Analysis error:', error);
      alert('Failed to analyze your data. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentQuestions = quizSections[currentSection];
  const progress = ((currentSection + 1) / quizSections.length) * 100;

  return (
    <div className="bg-lightblue min-h-screen">
      <Head>
        <title>Height Quiz - GenoBlueprint</title>
        <meta name="description" content="Discover your genetic potential with our comprehensive height and growth quiz." />
      </Head>

      {/* Header */}
      <header className="bg-deepblue text-white shadow">
        <div className="max-w-4xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="font-extrabold text-2xl tracking-tight">
            <span className="bg-gradient-to-r from-teal to-deepblue text-transparent bg-clip-text">GenoBlueprint</span>
          </div>
          <button 
            onClick={() => router.push('/')}
            className="text-teal hover:text-white transition"
          >
            ← Back to Home
          </button>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-deepblue">Progress</span>
            <span className="text-sm font-medium text-deepblue">{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-teal to-deepblue h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Privacy Notice */}
      <div className="max-w-4xl mx-auto px-6 py-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <span className="text-blue-600">🔒</span>
            <span className="text-sm font-semibold text-blue-800">Privacy First at GenoBlueprint</span>
          </div>
          <p className="text-xs text-blue-700 leading-relaxed">
            At GenoBlueprint, your privacy is our top priority. We do not sell, share, or trade your personal information, quiz answers, or photos with anyone. Your data is used only to generate your personalized growth and development report.
          </p>
        </div>
      </div>

      {/* Quiz Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-deepblue mb-6">{currentQuestions.title}</h2>

          {(currentQuestions as any).description && (
            <div className="bg-teal bg-opacity-10 border border-teal border-opacity-20 rounded-lg p-4 mb-6">
              <p className="text-deepblue text-sm">{(currentQuestions as any).description}</p>
            </div>
          )}

          <div className="space-y-6">
            {currentQuestions.questions.map((question, index) => (
              <div key={question.key} className="border-b border-gray-100 pb-6 last:border-b-0">
                <label className="block text-lg font-medium text-deepblue mb-3">
                  {question.question}
                </label>

                {question.type === 'radio' && (
                  <div className="space-y-2">
                    {question.options?.map((option) => (
                      <label key={option} className="flex items-center">
                        <input
                          type="radio"
                          name={question.key}
                          value={option}
                          checked={quizData[question.key as keyof QuizData] === option}
                          onChange={(e) => handleInputChange(question.key, e.target.value)}
                          className="mr-3 text-teal focus:ring-teal"
                        />
                        <span className="text-gray-700">{option}</span>
                      </label>
                    ))}
                  </div>
                )}

                {question.type === 'text' && (
                  <input
                    type="text"
                    placeholder={question.placeholder}
                    value={quizData[question.key as keyof QuizData] as string || ''}
                    onChange={(e) => handleInputChange(question.key, e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                  />
                )}

                {question.type === 'email' && (
                  <input
                    type="email"
                    placeholder={question.placeholder}
                    value={quizData[question.key as keyof QuizData] as string || ''}
                    onChange={(e) => handleInputChange(question.key, e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    required
                  />
                )}

                {question.type === 'checkbox' && (
                  <div className="space-y-2">
                    {question.options?.map((option) => (
                      <label key={option} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={quizData[question.key as keyof QuizData] === true}
                          onChange={(e) => handleInputChange(question.key, e.target.checked)}
                          className="mr-3 text-teal focus:ring-teal"
                        />
                        <span className="text-gray-700">{option}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <button
              onClick={handlePrevious}
              disabled={currentSection === 0}
              className="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            {currentSection === quizSections.length - 1 ? (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-8 py-2 bg-gradient-to-r from-teal to-deepblue text-white rounded-lg hover:from-teal-400 hover:to-deepblue transition disabled:opacity-50"
              >
                {isSubmitting ? 'Processing...' : 'Submit & See Your Report'}
              </button>
            ) : (
              <button
                onClick={handleNext}
                className="px-6 py-2 bg-gradient-to-r from-teal to-deepblue text-white rounded-lg hover:from-teal-400 hover:to-deepblue transition"
              >
                Next
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
