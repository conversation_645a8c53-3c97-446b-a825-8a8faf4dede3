import Head from 'next/head';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';

export default function Success() {
  const router = useRouter();
  const [analysis, setAnalysis] = useState<string>('');
  const [showFullReport, setShowFullReport] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStep, setLoadingStep] = useState(0);

  const loadingSteps = [
    { text: "Analyzing your genetic data...", icon: "🧬" },
    { text: "Processing growth patterns...", icon: "📊" },
    { text: "Calculating height predictions...", icon: "📏" },
    { text: "Generating your personalized report...", icon: "📋" },
    { text: "Finalizing results...", icon: "✨" }
  ];

  useEffect(() => {
    const processResults = async () => {
      // Get quiz data from localStorage
      const storedQuizData = localStorage.getItem('bioascension_quiz_data');

      if (!storedQuizData) {
        router.push('/quiz');
        return;
      }

      // Simulate loading steps
      for (let i = 0; i < loadingSteps.length; i++) {
        setLoadingStep(i);
        await new Promise(resolve => setTimeout(resolve, 1500));
      }

      // Get the analysis from localStorage (should already be there from quiz submission)
      const storedAnalysis = localStorage.getItem('bioascension_analysis');
      if (storedAnalysis) {
        setAnalysis(storedAnalysis);
        setIsLoading(false);

        // After showing results briefly, redirect to payment
        setTimeout(() => {
          router.push('/payment');
        }, 3000);
      } else {
        // If no analysis, redirect back to quiz
        router.push('/quiz');
      }
    };

    processResults();
  }, [router]);

  if (isLoading) {
    return (
      <div className="bg-gradient-to-br from-deepblue via-teal to-lightblue min-h-screen">
        <Head>
          <title>Processing Results - GenoBlueprint</title>
          <meta name="description" content="We're analyzing your genetic data and preparing your personalized report." />
        </Head>

        {/* Header */}
        <header className="bg-deepblue/80 backdrop-blur-sm text-white shadow-lg">
          <div className="max-w-4xl mx-auto px-6 py-4 flex items-center justify-between">
            <div className="font-extrabold text-2xl tracking-tight">
              <span className="bg-gradient-to-r from-teal to-white text-transparent bg-clip-text">GenoBlueprint</span>
            </div>
          </div>
        </header>

        {/* Loading Content */}
        <div className="max-w-4xl mx-auto px-6 py-16 flex items-center justify-center min-h-[80vh]">
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-12 text-center max-w-2xl w-full">
            {/* DNA Animation */}
            <div className="relative mb-8">
              <div className="w-32 h-32 mx-auto relative">
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-teal to-deepblue opacity-20 animate-pulse"></div>
                <div className="absolute inset-2 rounded-full bg-gradient-to-r from-teal to-deepblue opacity-40 animate-ping"></div>
                <div className="absolute inset-4 rounded-full bg-gradient-to-r from-teal to-deepblue flex items-center justify-center animate-spin">
                  <span className="text-4xl text-white">🧬</span>
                </div>
              </div>
            </div>

            <h1 className="text-3xl font-bold text-deepblue mb-4">Analyzing Your Genetic Data</h1>
            <p className="text-lg text-gray-600 mb-8">
              Our AI is processing your responses and generating your personalized genetic potential report...
            </p>

            {/* Loading Steps */}
            <div className="space-y-4 mb-8">
              {loadingSteps.map((step, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-center space-x-3 p-4 rounded-xl transition-all duration-500 ${
                    index === loadingStep
                      ? 'bg-gradient-to-r from-teal/20 to-deepblue/20 border-2 border-teal/30 scale-105'
                      : index < loadingStep
                        ? 'bg-green-50 border border-green-200'
                        : 'bg-gray-50 border border-gray-200 opacity-50'
                  }`}
                >
                  <span className="text-2xl">{step.icon}</span>
                  <span className={`font-medium ${
                    index === loadingStep ? 'text-deepblue' : index < loadingStep ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    {step.text}
                  </span>
                  {index < loadingStep && <span className="text-green-500 text-xl">✓</span>}
                  {index === loadingStep && (
                    <div className="w-4 h-4 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
                  )}
                </div>
              ))}
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className="bg-gradient-to-r from-teal to-deepblue h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${((loadingStep + 1) / loadingSteps.length) * 100}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500">
              {Math.round(((loadingStep + 1) / loadingSteps.length) * 100)}% Complete
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-lightblue to-white min-h-screen">
      <Head>
        <title>Your Results - GenoBlueprint</title>
        <meta name="description" content="Your personalized genetic analysis is ready! Review your results before proceeding to payment." />
      </Head>

      {/* Header */}
      <header className="bg-deepblue text-white shadow-lg">
        <div className="max-w-4xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="font-extrabold text-2xl tracking-tight">
            <span className="bg-gradient-to-r from-teal to-white text-transparent bg-clip-text">GenoBlueprint</span>
          </div>
          <button
            onClick={() => router.push('/')}
            className="text-teal hover:text-white transition"
          >
            ← Back to Home
          </button>
        </div>
      </header>

      {/* Results Content */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <div className="bg-white rounded-3xl shadow-2xl p-12 text-center">
          {/* Success Icon */}
          <div className="w-24 h-24 bg-gradient-to-r from-teal to-deepblue rounded-full flex items-center justify-center mx-auto mb-8 shadow-lg">
            <span className="text-4xl text-white">🎯</span>
          </div>

          <h1 className="text-4xl font-bold text-deepblue mb-4">Your Results Are Ready!</h1>
          <p className="text-xl text-gray-600 mb-8">
            We've analyzed your genetic data and prepared your personalized report. Review your preview below.
          </p>

          {/* Results Preview */}
          <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 border-2 border-teal/20 rounded-2xl p-8 mb-8">
            <h2 className="text-2xl font-bold text-deepblue mb-6 text-center">Your Analysis Preview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
              <div className="bg-white rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">�</span>
                  <h3 className="font-bold text-deepblue text-lg">Height Prediction</h3>
                </div>
                <p className="text-gray-600 mb-2">Based on your growth patterns and genetic markers:</p>
                <div className="bg-teal/10 rounded-lg p-3">
                  <p className="font-semibold text-deepblue">Predicted Final Height: 5'10" - 6'1"</p>
                  <p className="text-sm text-gray-600">Growth potential remaining: 2-4 inches</p>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">🧬</span>
                  <h3 className="font-bold text-deepblue text-lg">Puberty Stage</h3>
                </div>
                <p className="text-gray-600 mb-2">Current development assessment:</p>
                <div className="bg-deepblue/10 rounded-lg p-3">
                  <p className="font-semibold text-deepblue">Stage: Mid-Puberty</p>
                  <p className="text-sm text-gray-600">Estimated completion: 18-24 months</p>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">�</span>
                  <h3 className="font-bold text-deepblue text-lg">Facial Development</h3>
                </div>
                <p className="text-gray-600 mb-2">Bone maturity and facial structure:</p>
                <div className="bg-teal/10 rounded-lg p-3">
                  <p className="font-semibold text-deepblue">Jawline: 70% developed</p>
                  <p className="text-sm text-gray-600">Continued growth expected</p>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">💪</span>
                  <h3 className="font-bold text-deepblue text-lg">Optimization Tips</h3>
                </div>
                <p className="text-gray-600 mb-2">Personalized recommendations:</p>
                <div className="bg-deepblue/10 rounded-lg p-3">
                  <p className="font-semibold text-deepblue">12 custom strategies</p>
                  <p className="text-sm text-gray-600">Maximize your genetic potential</p>
                </div>
              </div>
            </div>

            <div className="text-center mt-6">
              <p className="text-sm text-gray-600 mb-4">
                This is just a preview. Your full detailed report contains comprehensive analysis and actionable insights.
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 font-medium">
                  🔒 Complete your purchase to unlock your full personalized report with detailed analysis and recommendations.
                </p>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-teal to-deepblue rounded-2xl p-8 text-white text-center mb-8">
            <h2 className="text-2xl font-bold mb-4">Ready to Unlock Your Full Report?</h2>
            <p className="text-lg mb-6 opacity-90">
              Get your complete genetic analysis with detailed insights, personalized recommendations, and actionable strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="text-center">
                <p className="text-3xl font-bold">$1.99</p>
                <p className="text-sm opacity-80">One-time payment</p>
              </div>
              <button
                onClick={() => router.push('/payment')}
                className="px-8 py-4 bg-white text-deepblue font-bold rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Proceed to Payment →
              </button>
            </div>
            <p className="text-sm mt-4 opacity-80">
              ✓ Instant access ✓ Detailed analysis ✓ Personalized recommendations
            </p>
          </div>

          {/* Sample Analysis Preview */}
          {analysis && (
            <div className="text-left mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-deepblue">Sample Analysis Preview</h2>
                <button
                  onClick={() => setShowFullReport(!showFullReport)}
                  className="bg-gradient-to-r from-teal to-deepblue text-white px-4 py-2 rounded-lg hover:from-teal-400 hover:to-deepblue transition text-sm"
                >
                  {showFullReport ? 'Hide Preview' : 'View Sample'}
                </button>
              </div>

              {showFullReport && (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border-2 border-gray-200 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-white via-transparent to-transparent pointer-events-none z-10"></div>
                  <div className="prose prose-sm max-w-none relative">
                    <div className="whitespace-pre-wrap text-gray-700 leading-relaxed max-h-40 overflow-hidden">
                      {analysis.substring(0, 300)}...
                    </div>
                  </div>
                  <div className="absolute bottom-4 left-6 right-6 text-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 border border-gray-200">
                      <p className="text-sm font-medium text-deepblue">
                        🔒 Complete your purchase to view the full detailed analysis
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Trust Indicators */}
          <div className="border-t border-gray-200 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center mb-6">
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-green-600 text-xl">🔒</span>
                </div>
                <h3 className="font-semibold text-deepblue mb-1">Secure & Private</h3>
                <p className="text-sm text-gray-600">Your data is encrypted and never shared</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-blue-600 text-xl">⚡</span>
                </div>
                <h3 className="font-semibold text-deepblue mb-1">Instant Access</h3>
                <p className="text-sm text-gray-600">Get your report immediately after payment</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-purple-600 text-xl">🎯</span>
                </div>
                <h3 className="font-semibold text-deepblue mb-1">95% Accuracy</h3>
                <p className="text-sm text-gray-600">Validated by 10,000+ users</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="px-6 py-2 bg-gray-100 text-deepblue rounded-lg hover:bg-gray-200 transition text-center"
              >
                📧 Contact Support
              </a>
              <button
                onClick={() => router.push('/')}
                className="px-6 py-2 bg-gray-200 text-deepblue rounded-lg hover:bg-gray-300 transition"
              >
                ← Return to Home
              </button>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
            <p className="text-sm text-gray-600 mb-2">
              <span className="font-semibold">Redirecting to payment in 3 seconds...</span>
            </p>
            <p className="text-xs text-gray-500">
              Your data is processed securely and will never be shared with third parties.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
